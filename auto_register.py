import requests
from bs4 import BeautifulSoup

urls = [
    "https://129.126.124.113:3000",
    "http://112.115.144.244:3000",
    "http://152.42.228.231:3000",
    "http://102.217.186.22:3000",
    "http://183.111.230.238:3000",
    "http://124.70.106.33:3000",
    "http://64.226.107.126:3389",
    "http://46.142.173.227:3000",
    "http://192.210.144.130:3000",
    "http://188.74.70.48:8080",
    "http://23.247.166.144:3000",
    "http://220.172.41.31:3000",
    "http://5.9.107.247:3000"
]

username = "admin"
email = "<EMAIL>"
password = "123456"

for url in urls:
    try:
        # 使用Session来保持cookies和其他状态信息
        session = requests.Session()

        # 第一步：注册
        registration_url = f'{url}/register'
        registration_data = {
            'username': username,
            'email': email,
            'password': password,
            'btn_register': 'Register'  # 假设此字段触发注册提交
        }
        registration_response = session.post(registration_url, data=registration_data, verify=False)

        if registration_response.status_code == 200:
            print(f"成功注册在 {url}")

            # 第二步：登录
            login_url = f'{url}/login'
            login_data = {
                'username': username,
                'password': password,
                'btn_login': 'Login'  # 假设此字段触发登录提交
            }
            login_response = session.post(login_url, data=login_data, verify=False)

            if login_response.status_code == 200:
                print(f"在{url}成功登录")

                # 第三步：提取模型信息
                # 假设有一个URL或某个部分可以访问到模型信息
                model_page_url = f'{url}/models'
                model_page_response = session.get(model_page_url, verify=False)
                soup = BeautifulSoup(model_page_response.text, 'html.parser')

                # 假设模型信息在类名为 "model_name" 的元素中
                models = soup.find_all(class_='model_name')
                for model in models:
                    print(f"在 {url} 发现模型：{model.text.strip()}")

        else:
            print(f"在 {url} 注册失败")
    
    except Exception as e:
        print(f"处理 {url} 时发生错误: {str(e)}")