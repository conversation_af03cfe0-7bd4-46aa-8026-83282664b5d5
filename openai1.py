from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import time

# 设置Chrome选项
chrome_options = Options()
chrome_options.add_argument("--headless")  # 如果你不需要看到浏览器窗口，可以启用无头模式
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")

# 替换为你的ChromeDriver路径
service = Service('/path/to/chromedriver')

# 定义要操作的网址列表
urls = [
    "https://129.126.124.113:3000",
    "http://112.115.144.244:3000",
    "http://152.42.228.231:3000",
    "http://102.217.186.22:3000",
    "http://183.111.230.238:3000",
    "http://124.70.106.33:3000",
    "http://64.226.107.126:3389",
    "http://46.142.173.227:3000",
    "http://192.210.144.130:3000",
    "http://188.74.70.48:8080",
    "http://23.247.166.144:3000",
    "http://220.172.41.31:3000",
    "http://5.9.107.247:3000"
]

# 用户信息
username = "admin"
email = "<EMAIL>"
password = "123456"

# 初始化Chrome驱动
driver = webdriver.Chrome(service=service, options=chrome_options)

for url in urls:
    try:
        driver.get(url)
        time.sleep(3)  # 等待页面加载

        # 找到注册表单并填写
        driver.find_element(By.NAME, 'username').send_keys(username)
        driver.find_element(By.NAME, 'email').send_keys(email)
        driver.find_element(By.NAME, 'password').send_keys(password)

        # 提交表单
        driver.find_element(By.NAME, 'submit').click()
        time.sleep(3)  # 等待注册过程

        # 登录
        driver.find_element(By.NAME, 'email').send_keys(email)
        driver.find_element(By.NAME, 'password').send_keys(password)
        driver.find_element(By.NAME, 'submit').click()
        time.sleep(3)  # 等待登录过程

        # 获取模型名称
        model_name = driver.find_element(By.ID, 'model_name').text
        print(f"URL: {url}, Model Name: {model_name}")

    except Exception as e:
        print(f"Failed to process URL: {url}\nError: {e}")

driver.quit()