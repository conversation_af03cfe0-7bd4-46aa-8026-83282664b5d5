import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Any
from collections import defaultdict, Counter
import csv

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SecurityReporter:
    """安全测试报告生成器"""
    
    def __init__(self):
        self.report_timestamp = datetime.now()
    
    def analyze_results(self, results: List[Dict]) -> Dict:
        """分析安全测试结果"""
        analysis = {
            'summary': {},
            'vulnerable_instances': [],
            'model_statistics': {},
            'security_issues': {},
            'recommendations': []
        }
        
        # 基础统计
        total_tested = len(results)
        successful_tests = len([r for r in results if not r.get('error')])
        registration_success = len([r for r in results if r.get('registration_test', {}).get('registration_success')])
        login_success = len([r for r in results if r.get('registration_test', {}).get('login_success')])
        
        analysis['summary'] = {
            'total_tested': total_tested,
            'successful_tests': successful_tests,
            'registration_success_count': registration_success,
            'login_success_count': login_success,
            'success_rate': f"{successful_tests/total_tested*100:.1f}%" if total_tested > 0 else "0%"
        }
        
        # 收集易受攻击的实例
        vulnerable_instances = []
        all_models = []
        security_issues = defaultdict(int)
        
        for result in results:
            if result.get('error'):
                continue
                
            url = result.get('url', 'Unknown')
            reg_test = result.get('registration_test', {})
            
            # 检查是否为易受攻击的实例
            is_vulnerable = False
            vulnerability_reasons = []
            
            if reg_test.get('registration_success') and reg_test.get('login_success'):
                is_vulnerable = True
                vulnerability_reasons.append("允许自由注册和登录")
            
            accessible_models = result.get('accessible_models', [])
            if accessible_models:
                is_vulnerable = True
                vulnerability_reasons.append(f"可访问 {len(accessible_models)} 个AI模型")
                all_models.extend([m.get('model_id', 'Unknown') for m in accessible_models])
            
            if is_vulnerable:
                vulnerable_instance = {
                    'url': url,
                    'reasons': vulnerability_reasons,
                    'user_role': reg_test.get('user_role', 'Unknown'),
                    'available_models_count': len(result.get('available_models', [])),
                    'accessible_models_count': len(accessible_models),
                    'accessible_models': [m.get('model_id', 'Unknown') for m in accessible_models],
                    'registration_identity': reg_test.get('identity', {})
                }
                vulnerable_instances.append(vulnerable_instance)
            
            # 统计安全问题
            for issue in result.get('security_issues', []):
                security_issues[issue] += 1
        
        analysis['vulnerable_instances'] = vulnerable_instances
        analysis['security_issues'] = dict(security_issues)
        
        # 模型统计
        model_counter = Counter(all_models)
        analysis['model_statistics'] = {
            'total_accessible_models': len(all_models),
            'unique_models': len(model_counter),
            'most_common_models': model_counter.most_common(10)
        }
        
        # 生成建议
        analysis['recommendations'] = self._generate_recommendations(analysis)
        
        return analysis
    
    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """生成安全建议"""
        recommendations = []
        
        vulnerable_count = len(analysis['vulnerable_instances'])
        if vulnerable_count > 0:
            recommendations.append(f"发现 {vulnerable_count} 个存在安全风险的Open WebUI实例")
            recommendations.append("建议联系这些实例的管理员，提醒他们更新安全配置")
        
        security_issues = analysis['security_issues']
        if "允许自由注册且无需审批" in security_issues:
            count = security_issues["允许自由注册且无需审批"]
            recommendations.append(f"有 {count} 个实例允许自由注册，建议启用管理员审批机制")
        
        if analysis['model_statistics']['total_accessible_models'] > 0:
            recommendations.append("建议实施适当的访问控制，限制未授权用户访问AI模型")
        
        return recommendations
    
    def generate_text_report(self, analysis: Dict) -> str:
        """生成文本格式的安全报告"""
        report = f"""
=== Open WebUI 安全研究报告 ===
生成时间: {self.report_timestamp.strftime('%Y-%m-%d %H:%M:%S')}

## 概要统计
- 总测试实例数: {analysis['summary']['total_tested']}
- 成功测试数: {analysis['summary']['successful_tests']}
- 成功注册数: {analysis['summary']['registration_success_count']}
- 成功登录数: {analysis['summary']['login_success_count']}
- 测试成功率: {analysis['summary']['success_rate']}

## 易受攻击的实例
发现 {len(analysis['vulnerable_instances'])} 个存在安全风险的实例:

"""
        
        for i, instance in enumerate(analysis['vulnerable_instances'], 1):
            report += f"{i}. {instance['url']}\n"
            report += f"   风险原因: {', '.join(instance['reasons'])}\n"
            report += f"   用户角色: {instance['user_role']}\n"
            report += f"   可访问模型数: {instance['accessible_models_count']}\n"
            
            if instance['accessible_models']:
                report += f"   可访问的模型: {', '.join(instance['accessible_models'][:5])}"
                if len(instance['accessible_models']) > 5:
                    report += f" (还有 {len(instance['accessible_models']) - 5} 个)"
                report += "\n"
            
            identity = instance['registration_identity']
            if identity:
                report += f"   测试账号: {identity.get('email', 'N/A')}:{identity.get('password', 'N/A')}\n"
            
            report += "\n"
        
        # 模型统计
        model_stats = analysis['model_statistics']
        report += f"""
## 模型访问统计
- 总可访问模型实例数: {model_stats['total_accessible_models']}
- 唯一模型数: {model_stats['unique_models']}

最常见的可访问模型:
"""
        
        for model, count in model_stats['most_common_models']:
            report += f"- {model}: {count} 个实例\n"
        
        # 安全问题统计
        report += "\n## 安全问题统计\n"
        for issue, count in analysis['security_issues'].items():
            report += f"- {issue}: {count} 个实例\n"
        
        # 建议
        report += "\n## 安全建议\n"
        for i, recommendation in enumerate(analysis['recommendations'], 1):
            report += f"{i}. {recommendation}\n"
        
        report += f"\n=== 报告结束 ===\n"
        
        return report
    
    def generate_csv_report(self, analysis: Dict, filename: str):
        """生成CSV格式的详细报告"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'URL', '风险等级', '注册成功', '登录成功', '用户角色',
                    '可访问模型数', '可访问模型列表', '测试邮箱', '测试密码', '风险原因'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for instance in analysis['vulnerable_instances']:
                    identity = instance.get('registration_identity', {})
                    writer.writerow({
                        'URL': instance['url'],
                        '风险等级': '高' if instance['accessible_models_count'] > 0 else '中',
                        '注册成功': '是',
                        '登录成功': '是',
                        '用户角色': instance['user_role'],
                        '可访问模型数': instance['accessible_models_count'],
                        '可访问模型列表': ', '.join(instance['accessible_models']),
                        '测试邮箱': identity.get('email', ''),
                        '测试密码': identity.get('password', ''),
                        '风险原因': '; '.join(instance['reasons'])
                    })
            
            logging.info(f"CSV报告已保存到: {filename}")
        except Exception as e:
            logging.error(f"生成CSV报告失败: {e}")
    
    def generate_json_report(self, analysis: Dict, filename: str):
        """生成JSON格式的详细报告"""
        try:
            report_data = {
                'metadata': {
                    'generated_at': self.report_timestamp.isoformat(),
                    'report_type': 'Open WebUI Security Assessment',
                    'version': '1.0'
                },
                'analysis': analysis
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"JSON报告已保存到: {filename}")
        except Exception as e:
            logging.error(f"生成JSON报告失败: {e}")
    
    def extract_credentials(self, analysis: Dict) -> List[Dict]:
        """提取所有成功的登录凭据"""
        credentials = []
        
        for instance in analysis['vulnerable_instances']:
            identity = instance.get('registration_identity', {})
            if identity:
                credential = {
                    'url': instance['url'],
                    'email': identity.get('email'),
                    'password': identity.get('password'),
                    'username': identity.get('username'),
                    'user_role': instance.get('user_role'),
                    'accessible_models_count': instance.get('accessible_models_count', 0)
                }
                credentials.append(credential)
        
        return credentials
    
    def save_credentials(self, analysis: Dict, filename: str):
        """保存登录凭据到文件"""
        credentials = self.extract_credentials(analysis)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=== Open WebUI 测试账号信息 ===\n\n")
                for i, cred in enumerate(credentials, 1):
                    f.write(f"实例 {i}:\n")
                    f.write(f"  URL: {cred['url']}\n")
                    f.write(f"  邮箱: {cred['email']}\n")
                    f.write(f"  密码: {cred['password']}\n")
                    f.write(f"  用户名: {cred['username']}\n")
                    f.write(f"  角色: {cred['user_role']}\n")
                    f.write(f"  可访问模型数: {cred['accessible_models_count']}\n")
                    f.write("  ---\n")
            
            logging.info(f"登录凭据已保存到: {filename}")
        except Exception as e:
            logging.error(f"保存登录凭据失败: {e}")


def generate_comprehensive_report(results: List[Dict], output_prefix: str = "security_report"):
    """生成综合安全报告"""
    reporter = SecurityReporter()
    
    # 分析结果
    analysis = reporter.analyze_results(results)
    
    # 生成各种格式的报告
    text_report = reporter.generate_text_report(analysis)
    
    # 保存文本报告
    with open(f"{output_prefix}.txt", 'w', encoding='utf-8') as f:
        f.write(text_report)
    
    # 保存其他格式报告
    reporter.generate_csv_report(analysis, f"{output_prefix}.csv")
    reporter.generate_json_report(analysis, f"{output_prefix}.json")
    reporter.save_credentials(analysis, f"{output_prefix}_credentials.txt")
    
    # 打印摘要
    print(text_report)
    
    logging.info(f"综合安全报告已生成，文件前缀: {output_prefix}")
    
    return analysis


if __name__ == "__main__":
    # 测试代码
    sample_results = [
        {
            'url': 'http://example.com:3000',
            'registration_test': {
                'registration_success': True,
                'login_success': True,
                'user_role': 'user',
                'identity': {'email': '<EMAIL>', 'password': '123456'}
            },
            'accessible_models': [
                {'model_id': 'gpt-3.5-turbo'},
                {'model_id': 'claude-3'}
            ],
            'security_issues': ['允许自由注册且无需审批', '发现 2 个可访问的AI模型']
        }
    ]
    
    generate_comprehensive_report(sample_results, "test_report")
