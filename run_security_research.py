#!/usr/bin/env python3
"""
Open WebUI 安全研究 - 快速运行脚本
"""

import logging
import time
from pathlib import Path

# 导入主要模块
from main_security_research import OpenWebUISecurityResearcher

def main():
    """运行Open WebUI安全研究"""
    
    print("=" * 60)
    print("Open WebUI 安全研究工具")
    print("=" * 60)
    print()
    
    # 配置信息
    FOFA_API_KEY = "1v43heo2ie8004lp"
    FOFA_EMAIL = "https://fofa.red"
    OUTPUT_DIR = f"security_research_{int(time.time())}"
    
    print(f"配置信息:")
    print(f"  FOFA API Key: {FOFA_API_KEY}")
    print(f"  FOFA URL: {FOFA_EMAIL}")
    print(f"  输出目录: {OUTPUT_DIR}")
    print()
    
    # 创建输出目录
    output_path = Path(OUTPUT_DIR)
    output_path.mkdir(exist_ok=True)
    
    try:
        # 创建研究器
        researcher = OpenWebUISecurityResearcher(FOFA_API_KEY, FOFA_EMAIL)
        
        print("开始安全研究流程...")
        print()
        
        # 运行完整研究
        analysis = researcher.run_full_research(OUTPUT_DIR)
        
        if analysis:
            print("\n" + "=" * 60)
            print("研究完成！主要发现:")
            print("=" * 60)
            
            summary = analysis['summary']
            vulnerable_instances = analysis['vulnerable_instances']
            model_stats = analysis['model_statistics']
            
            print(f"📊 测试统计:")
            print(f"   - 总测试实例: {summary['total_tested']}")
            print(f"   - 成功测试: {summary['successful_tests']}")
            print(f"   - 注册成功: {summary['registration_success_count']}")
            print(f"   - 登录成功: {summary['login_success_count']}")
            print()
            
            print(f"⚠️  安全风险:")
            print(f"   - 易受攻击实例: {len(vulnerable_instances)}")
            print(f"   - 可访问AI模型: {model_stats['total_accessible_models']}")
            print(f"   - 唯一模型类型: {model_stats['unique_models']}")
            print()
            
            if vulnerable_instances:
                print("🔍 发现的易受攻击实例:")
                for i, instance in enumerate(vulnerable_instances[:5], 1):  # 只显示前5个
                    print(f"   {i}. {instance['url']}")
                    print(f"      - 可访问模型: {instance['accessible_models_count']}")
                    identity = instance.get('registration_identity', {})
                    if identity:
                        print(f"      - 测试账号: {identity.get('email')}:{identity.get('password')}")
                
                if len(vulnerable_instances) > 5:
                    print(f"   ... 还有 {len(vulnerable_instances) - 5} 个实例")
                print()
            
            print(f"📁 详细结果已保存到: {OUTPUT_DIR}")
            print("   - 查看 05_final_report.txt 获取完整报告")
            print("   - 查看 05_final_report_credentials.txt 获取测试账号")
            print()
            
            # 显示最常见的模型
            if model_stats['most_common_models']:
                print("🤖 最常见的可访问模型:")
                for model, count in model_stats['most_common_models'][:5]:
                    print(f"   - {model}: {count} 个实例")
                print()
            
            print("=" * 60)
            print("安全研究完成！")
            print("=" * 60)
        
        else:
            print("❌ 研究未发现任何结果")
    
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 研究过程中发生错误: {e}")
        logging.error(f"研究失败: {e}", exc_info=True)


if __name__ == "__main__":
    main()
