import random
import string
import secrets
from typing import Dict, List
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class IdentityGenerator:
    """身份生成器 - 用于Open WebUI安全测试"""
    
    def __init__(self):
        # 常用的用户名前缀
        self.username_prefixes = [
            'admin', 'user', 'test', 'demo', 'guest', 'security', 'researcher',
            'analyst', 'tester', 'dev', 'qa', 'audit', 'check', 'verify'
        ]
        
        # 常用的邮箱域名（用于测试）
        self.email_domains = [
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'test.com', 'example.com', 'demo.org', 'temp.mail',
            'security.test', 'research.org'
        ]
        
        # 密码组成字符
        self.password_chars = string.ascii_letters + string.digits
        self.password_special_chars = '!@#$%^&*'
    
    def generate_username(self, length: int = None) -> str:
        """生成随机用户名"""
        if length is None:
            length = random.randint(6, 12)
        
        # 选择一个前缀
        prefix = random.choice(self.username_prefixes)
        
        # 添加随机数字或字母
        remaining_length = max(0, length - len(prefix))
        if remaining_length > 0:
            suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=remaining_length))
            username = prefix + suffix
        else:
            username = prefix
        
        return username[:length]  # 确保不超过指定长度
    
    def generate_email(self, username: str = None) -> str:
        """生成随机邮箱地址"""
        if username is None:
            username = self.generate_username()
        
        domain = random.choice(self.email_domains)
        
        # 有时在用户名后添加随机数字
        if random.random() < 0.3:
            username += str(random.randint(1, 999))
        
        return f"{username}@{domain}"
    
    def generate_password(self, length: int = 8, include_special: bool = True) -> str:
        """生成随机密码"""
        chars = self.password_chars
        if include_special:
            chars += self.password_special_chars
        
        # 确保密码包含至少一个大写字母、小写字母和数字
        password = [
            random.choice(string.ascii_uppercase),
            random.choice(string.ascii_lowercase),
            random.choice(string.digits)
        ]
        
        if include_special:
            password.append(random.choice(self.password_special_chars))
        
        # 填充剩余长度
        remaining_length = length - len(password)
        if remaining_length > 0:
            password.extend(random.choices(chars, k=remaining_length))
        
        # 打乱顺序
        random.shuffle(password)
        
        return ''.join(password)
    
    def generate_simple_password(self, length: int = 6) -> str:
        """生成简单密码（仅字母数字）"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    def generate_common_password(self) -> str:
        """生成常见的测试密码"""
        common_passwords = [
            '123456', 'password', '123456789', '12345678',
            'admin', 'admin123', 'password123', 'test123',
            'demo123', 'user123', '111111', '000000'
        ]
        return random.choice(common_passwords)
    
    def generate_identity(self, password_type: str = 'random') -> Dict[str, str]:
        """生成完整的身份信息"""
        username = self.generate_username()
        email = self.generate_email(username)
        
        if password_type == 'simple':
            password = self.generate_simple_password()
        elif password_type == 'common':
            password = self.generate_common_password()
        else:  # random
            password = self.generate_password()
        
        identity = {
            'username': username,
            'email': email,
            'password': password,
            'name': username.capitalize()  # 首字母大写作为显示名称
        }
        
        return identity
    
    def generate_multiple_identities(self, count: int = 5, password_type: str = 'random') -> List[Dict[str, str]]:
        """生成多个身份信息"""
        identities = []
        
        for i in range(count):
            identity = self.generate_identity(password_type)
            identities.append(identity)
        
        logging.info(f"生成了 {count} 个测试身份")
        return identities
    
    def generate_secure_token(self, length: int = 32) -> str:
        """生成安全的随机token"""
        return secrets.token_urlsafe(length)
    
    def generate_api_key(self, length: int = 40) -> str:
        """生成API密钥格式的字符串"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def save_identities_to_file(identities: List[Dict[str, str]], filename: str):
    """保存身份信息到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=== 生成的测试身份信息 ===\n\n")
            for i, identity in enumerate(identities, 1):
                f.write(f"身份 {i}:\n")
                f.write(f"  用户名: {identity['username']}\n")
                f.write(f"  邮箱: {identity['email']}\n")
                f.write(f"  密码: {identity['password']}\n")
                f.write(f"  显示名: {identity['name']}\n")
                f.write("  ---\n")
        
        logging.info(f"身份信息已保存到: {filename}")
    except Exception as e:
        logging.error(f"保存身份信息失败: {e}")


def load_identities_from_file(filename: str) -> List[Dict[str, str]]:
    """从文件加载身份信息"""
    identities = []
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            # 这里可以实现解析逻辑，暂时返回空列表
            pass
        
        logging.info(f"从文件加载了 {len(identities)} 个身份信息")
        return identities
    except Exception as e:
        logging.error(f"加载身份信息失败: {e}")
        return []


class IdentityPool:
    """身份池管理器"""
    
    def __init__(self):
        self.generator = IdentityGenerator()
        self.used_identities = set()
        self.available_identities = []
    
    def get_identity(self, password_type: str = 'random') -> Dict[str, str]:
        """获取一个未使用的身份"""
        if not self.available_identities:
            # 生成新的身份池
            self.available_identities = self.generator.generate_multiple_identities(10, password_type)
        
        identity = self.available_identities.pop(0)
        self.used_identities.add(identity['email'])
        
        return identity
    
    def is_identity_used(self, email: str) -> bool:
        """检查身份是否已使用"""
        return email in self.used_identities
    
    def mark_identity_used(self, email: str):
        """标记身份为已使用"""
        self.used_identities.add(email)
    
    def get_stats(self) -> Dict[str, int]:
        """获取身份池统计信息"""
        return {
            'used_count': len(self.used_identities),
            'available_count': len(self.available_identities)
        }


if __name__ == "__main__":
    # 测试代码
    generator = IdentityGenerator()
    
    # 生成单个身份
    print("=== 单个身份示例 ===")
    identity = generator.generate_identity()
    for key, value in identity.items():
        print(f"{key}: {value}")
    
    print("\n=== 多个身份示例 ===")
    identities = generator.generate_multiple_identities(3)
    for i, identity in enumerate(identities, 1):
        print(f"身份 {i}: {identity}")
    
    # 测试身份池
    print("\n=== 身份池测试 ===")
    pool = IdentityPool()
    test_identity = pool.get_identity()
    print(f"从池中获取的身份: {test_identity}")
    print(f"池统计: {pool.get_stats()}")
    
    # 保存到文件
    save_identities_to_file(identities, "test_identities.txt")
