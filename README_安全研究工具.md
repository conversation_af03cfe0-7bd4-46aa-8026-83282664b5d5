# Open WebUI 安全研究工具

这是一套用于Open WebUI安全研究的综合工具，帮助发现和分析Open WebUI实例的安全配置问题。

## 功能特性

- 🔍 **FOFA搜索**: 自动搜索互联网上的Open WebUI实例
- ✅ **URL验证**: 批量验证URL可访问性和去重
- 🔐 **安全测试**: 自动注册测试和模型访问检测
- 📊 **报告生成**: 生成详细的安全分析报告
- 🎯 **分批处理**: 智能分批避免API限制

## 文件结构

```
├── main_security_research.py    # 主程序
├── fofa_searcher.py            # FOFA搜索模块
├── url_validator.py            # URL验证模块
├── identity_generator.py       # 身份生成模块
├── enhanced_security_tester.py # 安全测试模块
├── security_reporter.py        # 报告生成模块
└── README_安全研究工具.md       # 使用说明
```

## 快速开始

### 1. 安装依赖

```bash
pip install requests beautifulsoup4
```

### 2. 运行完整研究

```bash
# 使用默认配置（您的FOFA API Key）
python main_security_research.py

# 或使用自定义配置
python main_security_research.py --fofa-key YOUR_API_KEY --output-dir custom_output
```

### 3. 从URL文件测试

```bash
python main_security_research.py --fofa-key YOUR_KEY --url-file urls.txt
```

## 输出文件说明

研究完成后会在输出目录生成以下文件：

- `01_raw_urls.txt` - FOFA搜索的原始URL列表
- `02_validation_results.json` - URL验证详细结果
- `03_accessible_openwebui.txt` - 可访问的Open WebUI实例
- `04_security_test_results.json` - 安全测试详细结果
- `05_final_report.txt` - 文本格式安全报告
- `05_final_report.csv` - CSV格式详细数据
- `05_final_report.json` - JSON格式完整数据
- `05_final_report_credentials.txt` - 测试账号信息

## 安全研究流程

1. **FOFA搜索阶段**
   - 使用多种搜索策略避免10000条限制
   - 按地区和端口分批搜索
   - 自动去重和格式化URL

2. **URL验证阶段**
   - 批量检测URL可访问性
   - 识别真正的Open WebUI实例
   - 过滤无效和重复URL

3. **安全测试阶段**
   - 自动生成测试身份
   - 尝试注册和登录
   - 检测可访问的AI模型
   - 分析安全配置问题

4. **报告生成阶段**
   - 统计分析测试结果
   - 识别安全风险实例
   - 生成多格式报告
   - 提供安全建议

## 配置说明

### FOFA配置
- API Key: `1v43heo2ie8004lp`
- URL: `https://fofa.red`

### 搜索策略
工具使用以下策略避免FOFA的10000条限制：
- 按国家地区分批搜索
- 按端口号分组查询
- 智能组合查询条件

### 安全测试配置
- 自动生成随机测试身份
- 支持多种密码策略
- 并发测试提高效率
- 详细记录测试结果

## 使用示例

### 基础使用
```python
from main_security_research import OpenWebUISecurityResearcher

# 创建研究器
researcher = OpenWebUISecurityResearcher(
    fofa_api_key="1v43heo2ie8004lp",
    fofa_email="https://fofa.red"
)

# 运行完整研究
analysis = researcher.run_full_research("my_research_output")
```

### 仅测试已知URL
```python
# 从文件加载URL进行测试
analysis = researcher.run_from_url_list("known_urls.txt", "test_output")
```

## 注意事项

⚠️ **重要提醒**：
- 此工具仅用于安全研究和漏洞发现
- 请遵守相关法律法规和道德准则
- 发现安全问题后应及时通知相关管理员
- 不得用于恶意攻击或非法用途

## 报告解读

### 风险等级
- **高风险**: 允许自由注册且可访问AI模型
- **中风险**: 允许注册但模型访问受限
- **低风险**: 需要管理员审批或无法访问

### 关键指标
- 注册成功率: 反映开放程度
- 模型访问数: 反映潜在风险
- 用户角色: 反映权限控制

## 技术特性

- 🚀 **高性能**: 多线程并发处理
- 🛡️ **容错性**: 完善的异常处理
- 📝 **详细日志**: 完整的操作记录
- 🔄 **重试机制**: 自动重试失败请求
- 📊 **多格式输出**: 支持TXT/CSV/JSON格式

## 扩展功能

工具支持以下扩展：
- 自定义搜索策略
- 添加新的安全检测项
- 集成其他搜索引擎
- 自定义报告模板

## 联系方式

如有问题或建议，请联系Open WebUI开发团队进行安全研究合作。

---

**免责声明**: 此工具仅用于合法的安全研究目的，使用者需自行承担使用责任。
