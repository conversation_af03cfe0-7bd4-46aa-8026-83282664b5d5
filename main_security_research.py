#!/usr/bin/env python3
"""
Open WebUI 安全研究主程序
用于发现和分析Open WebUI实例的安全配置问题
"""

import argparse
import logging
import sys
import time
from pathlib import Path

# 导入自定义模块
from fofa_searcher import FOFASearcher, save_urls_to_file, load_urls_from_file
from url_validator import URLValidator, save_validation_results, save_accessible_urls
from enhanced_security_tester import batch_security_test, save_security_results
from security_reporter import generate_comprehensive_report

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class OpenWebUISecurityResearcher:
    """Open WebUI安全研究器"""
    
    def __init__(self, fofa_api_key: str, fofa_email: str = "https://fofa.red"):
        self.fofa_searcher = FOFASearcher(fofa_api_key, fofa_email)
        self.url_validator = URLValidator()
        
    def run_full_research(self, output_dir: str = "security_research_output"):
        """运行完整的安全研究流程"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        logging.info("=== 开始Open WebUI安全研究 ===")
        
        # 步骤1: FOFA搜索
        logging.info("步骤1: 使用FOFA搜索Open WebUI实例...")
        urls = self.fofa_searcher.search_openwebui_instances()
        
        if not urls:
            logging.error("未发现任何Open WebUI实例，研究终止")
            return
        
        # 保存原始URL列表
        raw_urls_file = output_path / "01_raw_urls.txt"
        save_urls_to_file(urls, str(raw_urls_file))
        
        # 步骤2: URL验证和去重
        logging.info("步骤2: 验证URL可访问性...")
        unique_urls = self.url_validator.deduplicate_urls(urls)
        validation_results = self.url_validator.batch_validate_urls(unique_urls)
        
        # 保存验证结果
        validation_file = output_path / "02_validation_results.json"
        save_validation_results(validation_results, str(validation_file))
        
        # 筛选可访问的Open WebUI实例
        accessible_urls_file = output_path / "03_accessible_openwebui.txt"
        accessible_urls = save_accessible_urls(validation_results, str(accessible_urls_file))
        
        if not accessible_urls:
            logging.error("未发现可访问的Open WebUI实例，研究终止")
            return
        
        # 步骤3: 安全测试
        logging.info("步骤3: 执行安全测试...")
        security_results = batch_security_test(accessible_urls, max_workers=5)
        
        # 保存安全测试结果
        security_file = output_path / "04_security_test_results.json"
        save_security_results(security_results, str(security_file))
        
        # 步骤4: 生成报告
        logging.info("步骤4: 生成安全报告...")
        report_prefix = str(output_path / "05_final_report")
        analysis = generate_comprehensive_report(security_results, report_prefix)
        
        # 输出摘要
        self._print_research_summary(analysis, output_path)
        
        logging.info(f"=== 安全研究完成，结果保存在: {output_path} ===")
        
        return analysis
    
    def run_from_url_list(self, url_file: str, output_dir: str = "security_research_output"):
        """从URL列表文件运行安全研究"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        logging.info(f"从文件加载URL列表: {url_file}")
        urls = load_urls_from_file(url_file)
        
        if not urls:
            logging.error("URL文件为空或加载失败")
            return
        
        # 跳过FOFA搜索，直接进行验证和测试
        logging.info("验证URL可访问性...")
        unique_urls = self.url_validator.deduplicate_urls(urls)
        validation_results = self.url_validator.batch_validate_urls(unique_urls)
        
        # 保存验证结果
        validation_file = output_path / "validation_results.json"
        save_validation_results(validation_results, str(validation_file))
        
        # 筛选可访问的Open WebUI实例
        accessible_urls_file = output_path / "accessible_openwebui.txt"
        accessible_urls = save_accessible_urls(validation_results, str(accessible_urls_file))
        
        if not accessible_urls:
            logging.error("未发现可访问的Open WebUI实例")
            return
        
        # 安全测试
        logging.info("执行安全测试...")
        security_results = batch_security_test(accessible_urls, max_workers=5)
        
        # 保存结果
        security_file = output_path / "security_test_results.json"
        save_security_results(security_results, str(security_file))
        
        # 生成报告
        report_prefix = str(output_path / "security_report")
        analysis = generate_comprehensive_report(security_results, report_prefix)
        
        self._print_research_summary(analysis, output_path)
        
        return analysis
    
    def _print_research_summary(self, analysis: dict, output_path: Path):
        """打印研究摘要"""
        summary = analysis['summary']
        vulnerable_count = len(analysis['vulnerable_instances'])
        
        print(f"\n{'='*50}")
        print("Open WebUI 安全研究摘要")
        print(f"{'='*50}")
        print(f"测试实例总数: {summary['total_tested']}")
        print(f"成功测试数: {summary['successful_tests']}")
        print(f"发现易受攻击实例: {vulnerable_count}")
        print(f"可访问AI模型总数: {analysis['model_statistics']['total_accessible_models']}")
        print(f"结果保存目录: {output_path}")
        
        if vulnerable_count > 0:
            print(f"\n⚠️  发现 {vulnerable_count} 个存在安全风险的Open WebUI实例")
            print("详细信息请查看生成的报告文件")
        
        print(f"{'='*50}\n")


def main():
    parser = argparse.ArgumentParser(description='Open WebUI 安全研究工具')
    parser.add_argument('--fofa-key', required=True, help='FOFA API密钥')
    parser.add_argument('--fofa-email', default='https://fofa.red', help='FOFA邮箱/URL')
    parser.add_argument('--url-file', help='从文件加载URL列表（跳过FOFA搜索）')
    parser.add_argument('--output-dir', default='security_research_output', help='输出目录')
    parser.add_argument('--mode', choices=['full', 'test-only'], default='full', 
                       help='运行模式：full=完整流程，test-only=仅测试已知URL')
    
    args = parser.parse_args()
    
    try:
        researcher = OpenWebUISecurityResearcher(args.fofa_key, args.fofa_email)
        
        if args.url_file:
            # 从文件加载URL进行测试
            researcher.run_from_url_list(args.url_file, args.output_dir)
        else:
            # 运行完整研究流程
            researcher.run_full_research(args.output_dir)
            
    except KeyboardInterrupt:
        logging.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logging.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 如果直接运行，使用默认配置
    if len(sys.argv) == 1:
        # 使用您提供的配置
        researcher = OpenWebUISecurityResearcher(
            fofa_api_key="1v43heo2ie8004lp",
            fofa_email="https://fofa.red"
        )
        
        print("使用默认配置运行Open WebUI安全研究...")
        print("如需自定义配置，请使用命令行参数")
        print("例如: python main_security_research.py --fofa-key YOUR_KEY --output-dir custom_output")
        print()
        
        researcher.run_full_research()
    else:
        main()
