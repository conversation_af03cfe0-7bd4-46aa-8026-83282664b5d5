fofa:
title="open webui"

title="open webui" && country="CN" && fid="wU+V0ESN89Z/W27mP2PjzQ=="
title="open webui" && country="CN" && fid="IZM4ioT1NnCjGM2pdui2uA=="
title="open webui" && country="CN" && fid!="IZM4ioT1NnCjGM2pdui2uA=="&& fid!="wU+V0ESN89Z/W27mP2PjzQ=="
((title="open webui" && country!="CN" && country!="US" && country!="DE" && (port="443" || port="3000" || port="""5000")) && fid="wU+V0ESN89Z/W27mP2PjzQ==") && fid="htMCBuwGjvmoh5scSKo0lA=="
((title="open webui" && country!="CN" && country!="US" && country!="DE" && (port="443" || port="3000" || port="""5000")) && fid!="wU+V0ESN89Z/W27mP2PjzQ==") && fid!="htMCBuwGjvmoh5scSKo0lA=="
title="open webui" && country!="CN" && country!="US" && country!="DE" && port!="443" && port!="3000" && port!="5000" && fid="wU+V0ESN89Z/W27mP2PjzQ=="
title="open webui" && country!="CN" && country!="US" && country!="DE" && port!="443" && port!="3000" && port!="5000" && fid!="wU+V0ESN89Z/W27mP2PjzQ=="

好用：
title="open webui" && country="CN" && city!="Tianjin" 
title="open webui" && country="CN" && city="Tianjin"
title="open webui" && country!="CN" && country!="US" && country!="DE" && (port=443 || port=3000 || port=:5000) 
title="open webui" && country!="CN" && country!="US" && country!="DE" && port!=443 && port!=3000 && port!=5000








title="open webui" && country="CN"
title="open webui" && country="US"
title="open webui" && country="DE" 
title="open webui" && (country="GB" || country="IN" || country="JP" || country="FR" || country="IT" || country="CA" || country="KR" || country="RU" || country="AU" || country="ES" || country="NL" || country="CH" || country="SA" || country="TR" || country="SE" || country="PL" || country="BE" || country="TH" || country="ID" || country="AT" || country="IE" || country="NO" || country="IL" || country="BR" || country="DK" || country="SG" || country="MY" || country="ZA" || country="AR" || country="CO" || country="FI" || country="PT" || country="CL" || country="CZ" || country="GR" || country="IL" || country="HU" || country="QA" || country="NZ" || country="PH" || country="VN" || country="RO" || country="AE" || country="UA" || country="BD" || country="KW" || country="SK")




netsh int ipv4 add excludedportrange protocol=tcp startport=1200 numberofports=1 store=persistent

cd E:\chatgpt\openwebui\api_model_checker

python api_model_checker.py


整理：
帮我把这里面的BaseUrl地址后面加上/api/chat/completions。token的内容原封不动的复制出来。模型名字整理出来，去掉双引号，然后模型之间用英文逗号隔开。每个BaseUrl、token、模型放在一起作为一组。



帮我从文件中找出所有含有“gpt-4o”模型的组，把它们的BaseUrl地址后面加上/api/chat/completions。token的内容原封不动的复制出来。模型名字整理出来，去掉双引号，然后模型之间用英文逗号隔开。每个BaseUrl、token、模型放在一起作为一组。