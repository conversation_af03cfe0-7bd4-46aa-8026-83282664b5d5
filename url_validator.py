import concurrent.futures
import logging
import re
import time
from typing import List, Set, Dict, Tuple
from urllib.parse import urlparse, urljoin
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class URLValidator:
    """URL验证器 - 用于Open WebUI安全研究"""
    
    def __init__(self, timeout: int = 10, max_workers: int = 20):
        self.timeout = timeout
        self.max_workers = max_workers
        self.session = self._create_session()
        
    def _create_session(self) -> requests.Session:
        """创建带重试机制的Session"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置通用headers
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 禁用SSL验证（用于测试环境）
        session.verify = False
        
        return session
    
    def normalize_url(self, url: str) -> str:
        """标准化URL格式"""
        url = url.strip()
        
        # 如果没有协议，默认添加http://
        if not url.startswith(('http://', 'https://')):
            url = f'http://{url}'
        
        # 解析URL
        parsed = urlparse(url)
        
        # 重新构建标准化的URL
        normalized = f"{parsed.scheme}://{parsed.netloc}"
        if parsed.path and parsed.path != '/':
            normalized += parsed.path
        if parsed.query:
            normalized += f"?{parsed.query}"
            
        return normalized
    
    def deduplicate_urls(self, urls: List[str]) -> List[str]:
        """URL去重"""
        seen = set()
        unique_urls = []
        
        for url in urls:
            normalized = self.normalize_url(url)
            if normalized not in seen:
                seen.add(normalized)
                unique_urls.append(normalized)
        
        logging.info(f"去重前: {len(urls)} 个URL，去重后: {len(unique_urls)} 个URL")
        return unique_urls
    
    def check_url_accessibility(self, url: str) -> Dict:
        """检查URL可访问性"""
        result = {
            'url': url,
            'accessible': False,
            'status_code': None,
            'response_time': None,
            'title': None,
            'is_openwebui': False,
            'error': None
        }
        
        try:
            start_time = time.time()
            response = self.session.get(url, timeout=self.timeout, allow_redirects=True)
            response_time = time.time() - start_time
            
            result['accessible'] = True
            result['status_code'] = response.status_code
            result['response_time'] = round(response_time, 2)
            
            # 检查是否为Open WebUI
            content = response.text.lower()
            title_match = re.search(r'<title[^>]*>(.*?)</title>', content, re.IGNORECASE | re.DOTALL)
            if title_match:
                title = title_match.group(1).strip()
                result['title'] = title
                
                # 检查是否为Open WebUI实例
                openwebui_indicators = [
                    'open webui',
                    'openwebui',
                    'open-webui'
                ]
                
                if any(indicator in title.lower() for indicator in openwebui_indicators):
                    result['is_openwebui'] = True
                
                # 也检查页面内容
                if any(indicator in content for indicator in openwebui_indicators):
                    result['is_openwebui'] = True
            
        except requests.exceptions.Timeout:
            result['error'] = 'Timeout'
        except requests.exceptions.ConnectionError:
            result['error'] = 'Connection Error'
        except requests.exceptions.RequestException as e:
            result['error'] = str(e)
        except Exception as e:
            result['error'] = f'Unexpected error: {str(e)}'
        
        return result
    
    def batch_validate_urls(self, urls: List[str]) -> List[Dict]:
        """批量验证URL"""
        logging.info(f"开始验证 {len(urls)} 个URL...")
        
        results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_url = {executor.submit(self.check_url_accessibility, url): url for url in urls}
            
            # 收集结果
            for i, future in enumerate(concurrent.futures.as_completed(future_to_url), 1):
                try:
                    result = future.result()
                    results.append(result)
                    
                    if i % 50 == 0:  # 每50个URL报告一次进度
                        logging.info(f"已验证 {i}/{len(urls)} 个URL")
                        
                except Exception as e:
                    url = future_to_url[future]
                    logging.error(f"验证URL失败: {url}, 错误: {e}")
                    results.append({
                        'url': url,
                        'accessible': False,
                        'error': str(e)
                    })
        
        logging.info(f"URL验证完成，共验证 {len(results)} 个URL")
        return results
    
    def filter_accessible_openwebui(self, validation_results: List[Dict]) -> List[str]:
        """筛选出可访问的Open WebUI实例"""
        accessible_openwebui = []
        
        for result in validation_results:
            if result.get('accessible') and result.get('is_openwebui'):
                accessible_openwebui.append(result['url'])
        
        logging.info(f"发现 {len(accessible_openwebui)} 个可访问的Open WebUI实例")
        return accessible_openwebui
    
    def generate_validation_report(self, validation_results: List[Dict]) -> str:
        """生成验证报告"""
        total_urls = len(validation_results)
        accessible_count = sum(1 for r in validation_results if r.get('accessible'))
        openwebui_count = sum(1 for r in validation_results if r.get('is_openwebui'))
        
        report = f"""
=== URL验证报告 ===
总URL数量: {total_urls}
可访问URL数量: {accessible_count}
Open WebUI实例数量: {openwebui_count}
成功率: {accessible_count/total_urls*100:.1f}%

=== 可访问的Open WebUI实例 ===
"""
        
        for result in validation_results:
            if result.get('accessible') and result.get('is_openwebui'):
                report += f"URL: {result['url']}\n"
                report += f"  状态码: {result.get('status_code')}\n"
                report += f"  响应时间: {result.get('response_time')}s\n"
                report += f"  标题: {result.get('title', 'N/A')}\n"
                report += "  ---\n"
        
        return report


def save_validation_results(results: List[Dict], filename: str):
    """保存验证结果到文件"""
    try:
        import json
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logging.info(f"验证结果已保存到: {filename}")
    except Exception as e:
        logging.error(f"保存验证结果失败: {e}")


def save_accessible_urls(results: List[Dict], filename: str):
    """保存可访问的Open WebUI URL到文件"""
    try:
        accessible_openwebui = []
        for result in results:
            if result.get('accessible') and result.get('is_openwebui'):
                accessible_openwebui.append(result['url'])
        
        with open(filename, 'w', encoding='utf-8') as f:
            for url in accessible_openwebui:
                f.write(f"{url}\n")
        
        logging.info(f"已保存 {len(accessible_openwebui)} 个可访问的Open WebUI URL到: {filename}")
        return accessible_openwebui
    except Exception as e:
        logging.error(f"保存可访问URL失败: {e}")
        return []


if __name__ == "__main__":
    # 测试代码
    validator = URLValidator()
    
    # 示例URL列表
    test_urls = [
        "http://example.com:3000",
        "https://demo.openwebui.com",
        "http://localhost:8080"
    ]
    
    # 去重和标准化
    unique_urls = validator.deduplicate_urls(test_urls)
    
    # 批量验证
    results = validator.batch_validate_urls(unique_urls)
    
    # 生成报告
    report = validator.generate_validation_report(results)
    print(report)
