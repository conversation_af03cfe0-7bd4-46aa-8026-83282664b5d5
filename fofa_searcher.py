import base64
import json
import logging
import time
import urllib.parse
from typing import List, Dict, Set
import requests

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class FOFASearcher:
    """FOFA搜索器 - 用于Open WebUI安全研究"""
    
    def __init__(self, api_key: str, email: str = "https://fofa.red"):
        self.api_key = api_key
        self.email = email
        self.base_url = "https://fofa.red/api/v1/search/all"
        self.session = requests.Session()
        
    def encode_query(self, query: str) -> str:
        """Base64编码查询语句"""
        return base64.b64encode(query.encode()).decode()
    
    def search(self, query: str, size: int = 10000, page: int = 1) -> Dict:
        """执行FOFA搜索"""
        encoded_query = self.encode_query(query)
        
        params = {
            'email': self.email,
            'key': self.api_key,
            'qbase64': encoded_query,
            'size': min(size, 10000),  # FOFA限制单次最多10000条
            'page': page,
            'fields': 'host,ip,port,protocol,country,city,title'
        }
        
        try:
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logging.error(f"FOFA搜索失败: {e}")
            return {"error": True, "errmsg": str(e)}
    
    def batch_search_by_regions(self, base_query: str) -> List[str]:
        """按地区分批搜索，避免单次查询超过10000条限制"""
        all_urls = set()
        
        # 定义搜索策略 - 按地区和端口分批
        search_strategies = [
            # 中国地区细分
            f'{base_query} && country="CN" && city="Beijing"',
            f'{base_query} && country="CN" && city="Shanghai"', 
            f'{base_query} && country="CN" && city="Guangzhou"',
            f'{base_query} && country="CN" && city="Shenzhen"',
            f'{base_query} && country="CN" && city="Hangzhou"',
            f'{base_query} && country="CN" && city!="Beijing" && city!="Shanghai" && city!="Guangzhou" && city!="Shenzhen" && city!="Hangzhou"',
            
            # 美国地区
            f'{base_query} && country="US" && (port=443 || port=3000)',
            f'{base_query} && country="US" && port!=443 && port!=3000',
            
            # 德国地区  
            f'{base_query} && country="DE"',
            
            # 其他主要国家
            f'{base_query} && country="JP"',
            f'{base_query} && country="KR"',
            f'{base_query} && country="SG"',
            f'{base_query} && country="GB"',
            f'{base_query} && country="FR"',
            f'{base_query} && country="CA"',
            f'{base_query} && country="AU"',
            
            # 其他国家按端口分组
            f'{base_query} && country!="CN" && country!="US" && country!="DE" && country!="JP" && country!="KR" && country!="SG" && country!="GB" && country!="FR" && country!="CA" && country!="AU" && (port=443 || port=3000)',
            f'{base_query} && country!="CN" && country!="US" && country!="DE" && country!="JP" && country!="KR" && country!="SG" && country!="GB" && country!="FR" && country!="CA" && country!="AU" && port!=443 && port!=3000 && port!=80 && port!=8080',
            f'{base_query} && country!="CN" && country!="US" && country!="DE" && country!="JP" && country!="KR" && country!="SG" && country!="GB" && country!="FR" && country!="CA" && country!="AU" && (port=80 || port=8080)',
        ]
        
        for i, query in enumerate(search_strategies, 1):
            logging.info(f"执行搜索策略 {i}/{len(search_strategies)}: {query}")
            
            try:
                result = self.search(query, size=10000)
                
                if result.get("error"):
                    logging.error(f"搜索失败: {result.get('errmsg', 'Unknown error')}")
                    continue
                
                if "results" in result:
                    urls = self._extract_urls_from_results(result["results"])
                    all_urls.update(urls)
                    logging.info(f"本次搜索获得 {len(urls)} 个URL，累计 {len(all_urls)} 个")
                
                # 避免请求过于频繁
                time.sleep(2)
                
            except Exception as e:
                logging.error(f"搜索策略 {i} 执行失败: {e}")
                continue
        
        return list(all_urls)
    
    def _extract_urls_from_results(self, results: List[List]) -> Set[str]:
        """从FOFA搜索结果中提取URL"""
        urls = set()
        
        for result in results:
            try:
                # FOFA返回格式: [host, ip, port, protocol, country, city, title]
                if len(result) >= 4:
                    host = result[0]
                    protocol = result[3] if result[3] else "http"
                    
                    # 构建完整URL
                    if host:
                        # 如果host已经包含协议，直接使用
                        if host.startswith(('http://', 'https://')):
                            urls.add(host)
                        else:
                            # 否则根据协议构建URL
                            if protocol.lower() == "https":
                                urls.add(f"https://{host}")
                            else:
                                urls.add(f"http://{host}")
                                
            except Exception as e:
                logging.warning(f"解析结果失败: {e}, result: {result}")
                continue
        
        return urls
    
    def search_openwebui_instances(self) -> List[str]:
        """搜索Open WebUI实例"""
        base_query = 'title="Open WebUI"'
        
        logging.info("开始搜索Open WebUI实例...")
        urls = self.batch_search_by_regions(base_query)
        
        logging.info(f"搜索完成，共发现 {len(urls)} 个Open WebUI实例")
        return urls


def save_urls_to_file(urls: List[str], filename: str):
    """保存URL列表到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            for url in urls:
                f.write(f"{url}\n")
        logging.info(f"已保存 {len(urls)} 个URL到文件: {filename}")
    except Exception as e:
        logging.error(f"保存文件失败: {e}")


def load_urls_from_file(filename: str) -> List[str]:
    """从文件加载URL列表"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]
        logging.info(f"从文件加载了 {len(urls)} 个URL: {filename}")
        return urls
    except Exception as e:
        logging.error(f"加载文件失败: {e}")
        return []


if __name__ == "__main__":
    # 配置信息
    FOFA_API_KEY = "1v43heo2ie8004lp"  # 您提供的API Key
    FOFA_EMAIL = "https://fofa.red"    # 您提供的FOFA URL
    
    # 创建搜索器
    searcher = FOFASearcher(FOFA_API_KEY, FOFA_EMAIL)
    
    # 搜索Open WebUI实例
    urls = searcher.search_openwebui_instances()
    
    # 保存结果
    output_file = "openwebui_instances.txt"
    save_urls_to_file(urls, output_file)
    
    print(f"\n=== 搜索完成 ===")
    print(f"发现 {len(urls)} 个Open WebUI实例")
    print(f"结果已保存到: {output_file}")
