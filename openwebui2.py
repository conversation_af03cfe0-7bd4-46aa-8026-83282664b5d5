from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 设置webdriver
driver = webdriver.Chrome()  # 使用Chrome浏览器，你可以根据需要更换

# 网址列表
urls = [
    "https://129.126.124.113:3000",
    "http://112.115.144.244:3000",
    "http://152.42.228.231:3000",
    # 添加其他网址
]

for url in urls:
    try:
        driver.get(url)
        
        # 等待注册页面加载
        register_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[@id='register-button']"))  # 修改XPATH以匹配实际的注册按钮
        )
        
        register_button.click()
        
        # 填写注册信息
        username_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//input[@id='username']"))  # 修改XPATH以匹配实际的用户名输入框
        )
        username_input.send_keys("admin")
        
        email_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//input[@id='email']"))  # 修改XPATH以匹配实际的电子邮件输入框
        )
        email_input.send_keys("<EMAIL>")
        
        password_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//input[@id='password']"))  # 修改XPATH以匹配实际的密码输入框
        )
        password_input.send_keys("123456")
        
        # 提交注册表单
        submit_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[@id='submit-register']"))  # 修改XPATH以匹配实际的提交按钮
        )
        submit_button.click()
        
        # 登录
        # 这部分代码需要根据实际登录页面来修改
        
        # 提取模型名称
        # 这部分代码需要根据实际页面结构来修改
        
    except Exception as e:
        print(f"错误发生在{url}：{e}")
    
driver.quit()