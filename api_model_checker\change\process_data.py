import re
import sys
import chardet

def process_data(input_text):
    groups = input_text.split("-----------------------------")
    results = []

    for group in groups:
        base_url = re.search(r"BaseUrl: (http[s]?://\S+)", group)
        token = re.search(r"token: (\S+)", group)
        models = re.findall(r'"name": "([^"]+)"', group)

        if base_url and token and models:
            base_url = base_url.group(1) + "/api/chat/completions"
            token = token.group(1)
            models = ", ".join(models)
            results.append(f"BaseUrl: {base_url}\ntoken: {token}\n模型: {models}\n")

    if not results:
        print("警告：没有找到匹配的数据")
        return ""  # 返回空字符串而不是 None
    
    return "\n".join(results)

def read_file_with_auto_encoding(file_path):
    # 首先尝试 UTF-8
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except UnicodeDecodeError:
        pass

    # 然后尝试 GBK
    try:
        with open(file_path, 'r', encoding='gbk') as file:
            return file.read()
    except UnicodeDecodeError:
        pass

    # 最后使用 chardet 自动检测
    with open(file_path, 'rb') as file:
        raw_data = file.read()
        result = chardet.detect(raw_data)
        encoding = result['encoding']

    try:
        return raw_data.decode(encoding)
    except:
        print(f"无法解码文件。检测到的编码为: {encoding}")
        return None

try:
    input_text = read_file_with_auto_encoding('input_data.txt')

    if input_text is None:
        print("错误：无法读取输入文件")
        sys.exit(1)

    if not input_text.strip():
        print("错误：输入文件为空")
        sys.exit(1)

    processed_data = process_data(input_text)

    if not processed_data:
        print("错误：处理后的数据为空")
        sys.exit(1)

    with open('output_results.txt', 'w', encoding='utf-8') as file:
        file.write(processed_data)

    print("处理完成。结果已保存到 output_results.txt 文件中。")

except Exception as e:
    print(f"发生错误: {e}")
    sys.exit(1)