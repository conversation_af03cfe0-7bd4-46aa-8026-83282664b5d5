import concurrent.futures
import json
import logging
import time
from typing import List, Dict, Union, Optional
import requests
from requests.adapters import HTTPA<PERSON>pter
from urllib3.util.retry import Retry
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

from identity_generator import IdentityGenerator

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class EnhancedSecurityTester:
    """增强的Open WebUI安全测试器"""
    
    def __init__(self, timeout: int = 15, max_retries: int = 3):
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = self._create_session()
        self.identity_generator = IdentityGenerator()
        
    def _create_session(self) -> requests.Session:
        """创建带重试机制的Session"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置通用headers
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Content-Type': 'application/json'
        })
        
        # 禁用SSL验证
        session.verify = False
        
        return session
    
    def check_openwebui_endpoints(self, base_url: str) -> Dict:
        """检查Open WebUI的关键端点"""
        endpoints = {
            'signup': '/api/v1/auths/signup',
            'signin': '/api/v1/auths/signin',
            'models': '/api/models',
            'chat': '/api/chat/completions',
            'config': '/api/config',
            'version': '/api/version'
        }
        
        results = {}
        for name, endpoint in endpoints.items():
            try:
                url = f"{base_url.rstrip('/')}{endpoint}"
                response = self.session.get(url, timeout=self.timeout)
                results[name] = {
                    'accessible': True,
                    'status_code': response.status_code,
                    'response_size': len(response.content)
                }
            except Exception as e:
                results[name] = {
                    'accessible': False,
                    'error': str(e)
                }
        
        return results
    
    def test_registration(self, base_url: str, identity: Dict[str, str]) -> Dict:
        """测试注册功能"""
        signup_url = f"{base_url.rstrip('/')}/api/v1/auths/signup"
        
        signup_data = {
            "name": identity['name'],
            "email": identity['email'],
            "password": identity['password']
        }
        
        result = {
            'url': base_url,
            'identity': identity,
            'registration_success': False,
            'registration_response': None,
            'login_success': False,
            'token': None,
            'user_role': None,
            'error': None
        }
        
        try:
            # 尝试注册
            response = self.session.post(signup_url, json=signup_data, timeout=self.timeout)
            result['registration_response'] = response.json() if response.content else {}
            
            if response.status_code == 200:
                response_data = result['registration_response']
                
                if 'error' in response_data:
                    result['error'] = response_data['error']
                elif 'detail' in response_data:
                    if "already registered" in response_data['detail'].lower():
                        result['error'] = 'Email already registered'
                    else:
                        result['error'] = response_data['detail']
                else:
                    result['registration_success'] = True
                    result['user_role'] = response_data.get('role', 'unknown')
                    
                    # 如果角色是pending，不继续测试
                    if result['user_role'] == 'pending':
                        result['error'] = 'Registration pending approval'
                        return result
            
            # 尝试登录（无论注册是否成功）
            login_result = self.test_login(base_url, identity['email'], identity['password'])
            result.update(login_result)
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def test_login(self, base_url: str, email: str, password: str) -> Dict:
        """测试登录功能"""
        signin_url = f"{base_url.rstrip('/')}/api/v1/auths/signin"
        
        signin_data = {
            "email": email,
            "password": password
        }
        
        result = {
            'login_success': False,
            'token': None,
            'user_info': None
        }
        
        try:
            response = self.session.post(signin_url, json=signin_data, timeout=self.timeout)
            
            if response.status_code == 200:
                response_data = response.json()
                
                if 'token' in response_data:
                    result['login_success'] = True
                    result['token'] = response_data['token']
                    result['user_info'] = response_data
                    
        except Exception as e:
            result['login_error'] = str(e)
        
        return result
    
    def get_available_models(self, base_url: str, token: str) -> List[Dict]:
        """获取可用模型列表"""
        models_url = f"{base_url.rstrip('/')}/api/models"
        
        headers = {
            'Authorization': f'Bearer {token}'
        }
        
        try:
            response = self.session.get(models_url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('data', [])
                
        except Exception as e:
            logging.error(f"获取模型列表失败: {e}")
        
        return []
    
    def test_model_access(self, base_url: str, token: str, model_id: str) -> Dict:
        """测试模型访问"""
        chat_url = f"{base_url.rstrip('/')}/api/chat/completions"
        
        headers = {
            'Authorization': f'Bearer {token}'
        }
        
        test_data = {
            "model": model_id,
            "messages": [{"role": "user", "content": "Hello, this is a security test."}],
            "stream": False,
            "max_tokens": 10
        }
        
        result = {
            'model_id': model_id,
            'accessible': False,
            'response': None,
            'error': None
        }
        
        try:
            response = self.session.post(chat_url, headers=headers, json=test_data, timeout=self.timeout)
            
            if response.status_code == 200:
                result['accessible'] = True
                result['response'] = response.json()
            else:
                result['error'] = f"HTTP {response.status_code}"
                
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def comprehensive_security_test(self, base_url: str) -> Dict:
        """综合安全测试"""
        logging.info(f"开始对 {base_url} 进行综合安全测试...")
        
        # 生成测试身份
        identity = self.identity_generator.generate_identity('simple')
        
        result = {
            'url': base_url,
            'timestamp': time.time(),
            'endpoints_check': {},
            'registration_test': {},
            'available_models': [],
            'accessible_models': [],
            'security_issues': []
        }
        
        try:
            # 1. 检查端点可访问性
            result['endpoints_check'] = self.check_openwebui_endpoints(base_url)
            
            # 2. 测试注册和登录
            registration_result = self.test_registration(base_url, identity)
            result['registration_test'] = registration_result
            
            # 3. 如果登录成功，测试模型访问
            if registration_result.get('login_success') and registration_result.get('token'):
                token = registration_result['token']
                
                # 获取模型列表
                models = self.get_available_models(base_url, token)
                result['available_models'] = models
                
                # 测试模型访问
                accessible_models = []
                for model in models[:5]:  # 只测试前5个模型
                    model_test = self.test_model_access(base_url, token, model.get('id', ''))
                    if model_test['accessible']:
                        accessible_models.append(model_test)
                
                result['accessible_models'] = accessible_models
            
            # 4. 安全问题分析
            security_issues = self._analyze_security_issues(result)
            result['security_issues'] = security_issues
            
        except Exception as e:
            result['error'] = str(e)
            logging.error(f"综合安全测试失败: {e}")
        
        return result
    
    def _analyze_security_issues(self, test_result: Dict) -> List[str]:
        """分析安全问题"""
        issues = []
        
        # 检查是否允许自由注册
        reg_test = test_result.get('registration_test', {})
        if reg_test.get('registration_success'):
            if reg_test.get('user_role') != 'pending':
                issues.append("允许自由注册且无需审批")
        
        # 检查是否有可访问的模型
        accessible_models = test_result.get('accessible_models', [])
        if accessible_models:
            issues.append(f"发现 {len(accessible_models)} 个可访问的AI模型")
        
        # 检查端点暴露情况
        endpoints = test_result.get('endpoints_check', {})
        exposed_endpoints = [name for name, info in endpoints.items() if info.get('accessible')]
        if len(exposed_endpoints) > 3:
            issues.append(f"暴露了 {len(exposed_endpoints)} 个API端点")
        
        return issues


def batch_security_test(urls: List[str], max_workers: int = 10) -> List[Dict]:
    """批量安全测试"""
    tester = EnhancedSecurityTester()
    results = []
    
    logging.info(f"开始批量安全测试，共 {len(urls)} 个URL...")
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_url = {executor.submit(tester.comprehensive_security_test, url): url for url in urls}
        
        for i, future in enumerate(concurrent.futures.as_completed(future_to_url), 1):
            try:
                result = future.result()
                results.append(result)
                
                if i % 10 == 0:
                    logging.info(f"已完成 {i}/{len(urls)} 个URL的安全测试")
                    
            except Exception as e:
                url = future_to_url[future]
                logging.error(f"测试URL失败: {url}, 错误: {e}")
                results.append({
                    'url': url,
                    'error': str(e),
                    'timestamp': time.time()
                })
    
    logging.info(f"批量安全测试完成，共测试 {len(results)} 个URL")
    return results


def save_security_results(results: List[Dict], filename: str):
    """保存安全测试结果"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logging.info(f"安全测试结果已保存到: {filename}")
    except Exception as e:
        logging.error(f"保存安全测试结果失败: {e}")


if __name__ == "__main__":
    # 测试代码
    tester = EnhancedSecurityTester()
    
    # 单个URL测试
    test_url = "http://example.com:3000"
    result = tester.comprehensive_security_test(test_url)
    print(json.dumps(result, indent=2, ensure_ascii=False))
